{% extends 'base.html.twig' %}

{% block title %}{{ space.name }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
    <script src="{{ asset('js/reservation-date-picker.js') }}"></script>
    <script>
        // Booked dates for chaque desk (optionnel si déjà géré dans chaque modal)
        window.bookedDates = window.bookedDates || {};
        {% for desk in desks %}
        window.bookedDates[{{ desk.id }}] = {{ (desk.bookedDates ?? [])|json_encode|raw }};
        {% endfor %}
    </script>
{% endblock %}
{% block content %}
    <div class="container mx-auto">
        <div class="mb-6">
            <a href="{{ path('app_space_index') }}" class="text-blue-600 hover:underline flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to Spaces
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-8">
            <div class="p-6">
                <h1 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">{{ space.name }}</h1>

                <div class="mb-6">
                    <p class="text-gray-700 dark:text-gray-300">{{ space.description }}</p>
                </div>

                <div class="flex flex-wrap gap-4 mb-6">
                    <div class="flex items-center text-gray-700 dark:text-gray-300">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Host: {{ space.host.firstname }} {{ space.host.lastname }}
                    </div>

                    <div class="flex items-center text-gray-700 dark:text-gray-300">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        {{ space.address.street }}, {{ space.address.city }}
                    </div>
                </div>

                {% if availability %}
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">Disponibilités</h3>
                        <div class="flex flex-wrap gap-1 mb-3">
                            {% set days = {
                                'monday': 'Lun',
                                'tuesday': 'Mar',
                                'wednesday': 'Mer',
                                'thursday': 'Jeu',
                                'friday': 'Ven',
                                'saturday': 'Sam',
                                'sunday': 'Dim'
                            } %}

                            {% for key, day in days %}
                                {% set isAvailable = attribute(availability, 'is' ~ key|capitalize) %}
                                <div class="flex flex-col items-center">
                                    <div class="w-10 h-10 flex items-center justify-center rounded-lg {% if isAvailable %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300{% else %}bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500{% endif %}">
                                        {{ day|slice(0, 1) }}
                                    </div>
                                    <span class="text-xs mt-1 {% if isAvailable %}text-blue-800 dark:text-blue-300{% else %}text-gray-400 dark:text-gray-500{% endif %}">{{ day }}</span>
                                </div>
                            {% endfor %}
                        </div>
                        <p class="text-gray-700 dark:text-gray-300">Horaires: {{ availability.openingTime|date('H:i') }} - {{ availability.closingTime|date('H:i') }}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Desks</h2>
            {% if is_host() and space.host == app.user %}
                <button
                        type="button"
                        class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800"
                        data-modal-target="deskModal"
                        data-modal-toggle="deskModal"
                >
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Desk
                </button>
            {% endif %}
        </div>
        <div id="desks-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for desk in desks %}
                {% if desk.isAvailable %}
                    {% include 'space/deskDetails.html.twig' with {'desk': desk, 'reserveButton' : true } %}
                {% endif %}
            {% else %}
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-700 dark:text-gray-300">No desks available in this space.</p>
                </div>
            {% endfor %}
        </div>
    </div>
    {{ component('desk_modal', { space: space }) }}

    {# Add reservation modals for each desk #}
    {% for desk in desks %}
        {% if desk.isAvailable %}
            {{ component('reservation_modal', { desk: desk }) }}
        {% endif %}
    {% endfor %}

    <script>
        // Set up booked dates for each desk
        window.bookedDates = window.bookedDates || {};
        {% for desk in desks %}
            {% if desk.isAvailable %}
                // Get booked dates for desk {{ desk.id }}
                {% set existingReservations = desk.reservations|filter(r => r.status != 2) %}
                {% set bookedDatesForDesk = [] %}
                {% for reservation in existingReservations %}
                    {% set bookedDatesForDesk = bookedDatesForDesk|merge([reservation.reservationDate|date('Y-m-d')]) %}
                {% endfor %}
                window.bookedDates[{{ desk.id }}] = {{ bookedDatesForDesk|json_encode|raw }};
            {% endif %}
        {% endfor %}
    </script>
{% endblock %}