{% extends 'base.html.twig' %}

{% block title %}My Reservations{% endblock %}

{% block content %}
            <div class="container mx-auto">
                <h1 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">My Reservations</h1>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-6 py-3">Space</th>
                                    <th scope="col" class="px-6 py-3">Desk</th>
                                    <th scope="col" class="px-6 py-3">Date</th>
                                    <th scope="col" class="px-6 py-3">Status</th>
                                    <th scope="col" class="px-6 py-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reservation in reservations %}
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">
                                            {{ reservation.desk.space.name }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ reservation.desk.name }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ reservation.reservationDate ? reservation.reservationDate|date('Y-m-d') : 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ constant('App\\Entity\\Reservation::RESERVATION_STATUSES')[reservation.status] }}
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex space-x-2">
                                                <a href="{{ path('app_reservation_show', {'id': reservation.id}) }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">View</a>
                                                {% if reservation.status != 2 %}
                                                    <a href="{{ path('app_reservation_cancel', {'id': reservation.id}) }}" class="font-medium text-red-600 dark:text-red-500 hover:underline">Cancel</a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% else %}
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td colspan="5" class="px-6 py-4 text-center">
                                            No reservations found. <a href="{{ path('app_space_index') }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Browse spaces</a> to make a reservation.
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
{% endblock %}
