// Calendar styles for Flatpickr date picker

// General Flatpickr styling improvements
.flatpickr-calendar {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    padding: 0.5rem;
    width: 325px;
    border: 1px solid #e5e7eb;
    font-family: inherit;
}

.flatpickr-months {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.flatpickr-month {
    height: 40px;
}

.flatpickr-current-month {
    padding-top: 0;
    font-size: 1rem;
}

.flatpickr-weekday {
    font-size: 0.875rem;
    font-weight: 500;
    background: transparent;
}

.flatpickr-day {
    border-radius: 0.375rem;
    margin: 2px;
    height: 38px;
    line-height: 38px;
    font-size: 0.875rem;
    max-width: 38px;
}

.flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange {
    background: #3b82f6;
    border-color: #3b82f6;
    box-shadow: none;
}

.flatpickr-day.selected:hover, .flatpickr-day.startRange:hover, .flatpickr-day.endRange:hover {
    background: #2563eb;
    border-color: #2563eb;
}

.flatpickr-day:hover {
    background: #e5e7eb;
    border-color: #e5e7eb;
}

.flatpickr-day.today {
    border-color: #3b82f6;
}

.flatpickr-day.today:hover {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

// Available days styling
.flatpickr-day.available-day {
    background-color: #e0f2fe;
    border-color: #e0f2fe;
    color: #0369a1;
}

.flatpickr-day.available-day:hover {
    background-color: #bae6fd;
    border-color: #bae6fd;
}

// Disabled days styling
.flatpickr-day.disabled, .flatpickr-day.disabled:hover {
    color: rgba(57, 57, 57, 0.3);
    background: rgba(57, 57, 57, 0.05);
    cursor: not-allowed;
    text-decoration: line-through;
}

// Dark mode styles
.dark .flatpickr-calendar {
    background: #1f2937;
    color: #f3f4f6;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    border: 1px solid #374151;
}

.dark .flatpickr-day {
    color: #f3f4f6;
    border-color: transparent;
}

.dark .flatpickr-day:hover {
    background: #374151;
}

.dark .flatpickr-day.disabled, .dark .flatpickr-day.disabled:hover {
    color: rgba(243, 244, 246, 0.3);
    background: rgba(243, 244, 246, 0.05);
}

.dark .flatpickr-day.selected, .dark .flatpickr-day.startRange, .dark .flatpickr-day.endRange {
    background: #3b82f6;
    border-color: #3b82f6;
}

.dark .flatpickr-day.selected:hover, .dark .flatpickr-day.startRange:hover, .dark .flatpickr-day.endRange:hover {
    background: #2563eb;
    border-color: #2563eb;
}

.dark .flatpickr-day.today {
    border-color: #3b82f6;
}

.dark .flatpickr-day.today:hover {
    background: #3b82f6;
    color: white;
}

// Dark mode available days
.dark .flatpickr-day.available-day {
    background-color: #0c4a6e;
    border-color: #0c4a6e;
    color: #bae6fd;
}

.dark .flatpickr-day.available-day:hover {
    background-color: #075985;
    border-color: #075985;
}

.dark .flatpickr-months .flatpickr-month,
.dark .flatpickr-months .flatpickr-prev-month,
.dark .flatpickr-months .flatpickr-next-month {
    color: #f3f4f6;
    fill: #f3f4f6;
}

.dark .flatpickr-months .flatpickr-prev-month:hover svg,
.dark .flatpickr-months .flatpickr-next-month:hover svg {
    fill: #3b82f6;
}

.dark .flatpickr-weekday {
    color: #9ca3af;
}

.dark .flatpickr-current-month .numInputWrapper span.arrowUp:after {
    border-bottom-color: #f3f4f6;
}

.dark .flatpickr-current-month .numInputWrapper span.arrowDown:after {
    border-top-color: #f3f4f6;
}

// Calendar container styles
#calendar-container {
    margin-bottom: 1.5rem;
}

// Hide the original input completely
#reservation_form_reservationDate {
    display: none !important;
    visibility: hidden !important;
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border-width: 0 !important;
}

// Styles for the calendar container
.flatpickr-calendar-container {
    width: 100%;
    max-width: 325px;
    margin: 0 auto;
}

// Styles for the inline calendar
.flatpickr-calendar.inline {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin: 0 auto 1rem auto;
    width: 100%;
}

// Add some spacing between the calendar and other elements
.flatpickr-calendar.inline + div {
    margin-top: 1rem;
}

// Make the calendar more responsive
@media (max-width: 640px) {
    .flatpickr-calendar-container,
    .flatpickr-calendar.inline {
        max-width: 100%;
    }
}

// Add a title to the calendar
.flatpickr-calendar.inline::before {
    content: "Select a date";
    display: block;
    text-align: center;
    font-weight: 500;
    padding: 0.5rem 0;
    color: #374151;
}

.dark .flatpickr-calendar.inline::before {
    color: #f3f4f6;
}

// Custom inline datepicker styles
.inline-datepicker-container {
    .inline-datepicker {
        width: 100%;
        max-width: 320px;
        margin: 0 auto;

        .datepicker-header {
            .month-year {
                min-width: 150px;
                text-align: center;
            }

            .prev-btn, .next-btn {
                transition: all 0.2s ease;

                &:hover {
                    transform: scale(1.05);
                }

                svg {
                    transition: transform 0.2s ease;
                }

                &:hover svg {
                    transform: scale(1.1);
                }
            }
        }

        .days-of-week {
            span {
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.025em;
            }
        }

        .calendar-grid {
            button {
                position: relative;
                font-weight: 500;
                transition: all 0.15s ease;

                &:not(:disabled):hover {
                    transform: scale(1.05);
                }

                &.selected-date {
                    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
                    transform: scale(1.05);
                }

                &:disabled {
                    opacity: 0.4;
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 10%;
                        right: 10%;
                        height: 1px;
                        background-color: currentColor;
                        opacity: 0.6;
                    }
                }
            }
        }
    }
}

// Animation for calendar appearance
.inline-datepicker-container .inline-datepicker {
    animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

// Responsive adjustments
@media (max-width: 640px) {
    .inline-datepicker-container .inline-datepicker {
        max-width: 100%;
        padding: 1rem;

        .calendar-grid button {
            height: 2.5rem;
            width: 2.5rem;
            font-size: 0.875rem;
        }

        .days-of-week span {
            padding: 0.5rem;
            font-size: 0.75rem;
        }
    }
}
