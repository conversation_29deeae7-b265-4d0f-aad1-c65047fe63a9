import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static values = { bookedDates: Array }

    connect() {
        this.form = this.element.querySelector('form');
        this.deskId = this.element.id.replace('reservationModal-', '');

        if (this.form) {
            this.form.addEventListener('submit', this.handleFormSubmit.bind(this));
        } else {
            console.error('Form not found in modal');
        }

        setTimeout(() => {
            this.initFlowbiteDatepicker();
        }, 100);
    }

    handleFormSubmit(event) {
        const dateInput = this.element.querySelector(`#reservation_date_${this.deskId}`);
        if (!dateInput || !dateInput.value) {
            event.preventDefault();
            alert('Veuillez sélectionner une date pour votre réservation');
            return false;
        }
    }

    initFlowbiteDatepicker() {
        const dateInput = this.element.querySelector(`#reservation_date_${this.deskId}`);
        const inlineContainer = this.element.querySelector(`#datepicker-inline-${this.deskId}`);
        
        if (!dateInput || !inlineContainer) {
            console.error('Date input or inline container not found');
            return;
        }

        // Get booked dates
        const bookedDates = this.bookedDatesValue || [];

        // Generate disabled dates (past dates and booked dates)
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Create the inline datepicker HTML structure directly
        inlineContainer.innerHTML = `
            <div class="inline-datepicker bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 shadow-lg max-w-sm mx-auto">
                <div class="datepicker-header flex items-center justify-between mb-4">
                    <button type="button" class="prev-btn p-2 text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div class="month-year text-lg font-semibold text-gray-900 dark:text-white"></div>
                    <button type="button" class="next-btn p-2 text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
                <div class="days-of-week grid grid-cols-7 mb-2">
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400 text-center py-2">Lu</span>
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400 text-center py-2">Ma</span>
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400 text-center py-2">Me</span>
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400 text-center py-2">Je</span>
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400 text-center py-2">Ve</span>
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400 text-center py-2">Sa</span>
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400 text-center py-2">Di</span>
                </div>
                <div class="calendar-grid grid grid-cols-7 gap-1" id="calendar-grid-${this.deskId}">
                    <!-- Calendar days will be generated here -->
                </div>
            </div>
        `;

        // Initialize the calendar
        this.initializeCalendar(inlineContainer, dateInput, bookedDates, today);
    }

    initializeCalendar(container, dateInput, bookedDates, today) {
        this.currentMonth = today.getMonth();
        this.currentYear = today.getFullYear();
        
        this.renderCalendar(container, dateInput, bookedDates, today);
        
        // Add navigation event listeners
        const prevBtn = container.querySelector('.prev-btn');
        const nextBtn = container.querySelector('.next-btn');
        
        prevBtn.addEventListener('click', () => {
            this.currentMonth--;
            if (this.currentMonth < 0) {
                this.currentMonth = 11;
                this.currentYear--;
            }
            this.renderCalendar(container, dateInput, bookedDates, today);
        });
        
        nextBtn.addEventListener('click', () => {
            this.currentMonth++;
            if (this.currentMonth > 11) {
                this.currentMonth = 0;
                this.currentYear++;
            }
            this.renderCalendar(container, dateInput, bookedDates, today);
        });
    }

    renderCalendar(container, dateInput, bookedDates, today) {
        const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
            'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
        
        // Update month/year display
        const monthYearDisplay = container.querySelector('.month-year');
        monthYearDisplay.textContent = `${monthNames[this.currentMonth]} ${this.currentYear}`;
        
        // Get calendar grid
        const grid = container.querySelector(`#calendar-grid-${this.deskId}`);
        grid.innerHTML = '';
        
        // Get first day of month and number of days
        const firstDay = new Date(this.currentYear, this.currentMonth, 1);
        const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = (firstDay.getDay() + 6) % 7; // Convert to Monday = 0
        
        // Add empty cells for days before the first day of the month
        for (let i = 0; i < startingDayOfWeek; i++) {
            const emptyCell = document.createElement('div');
            emptyCell.className = 'h-8';
            grid.appendChild(emptyCell);
        }
        
        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(this.currentYear, this.currentMonth, day);
            const dateString = date.toISOString().slice(0, 10);
            const isPast = date < today;
            const isBooked = bookedDates.includes(dateString);
            const isToday = dateString === today.toISOString().slice(0, 10);
            
            const dayCell = document.createElement('button');
            dayCell.type = 'button';
            dayCell.textContent = day;
            dayCell.className = `h-8 w-8 text-sm rounded-lg flex items-center justify-center transition-colors ${
                isPast || isBooked 
                    ? 'text-gray-300 dark:text-gray-600 cursor-not-allowed bg-gray-50 dark:bg-gray-800' 
                    : 'text-gray-900 dark:text-white hover:bg-blue-100 dark:hover:bg-blue-900 cursor-pointer'
            } ${isToday ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' : ''}`;
            
            if (!isPast && !isBooked) {
                dayCell.addEventListener('click', () => {
                    // Remove previous selection
                    container.querySelectorAll('.selected-date').forEach(cell => {
                        cell.classList.remove('selected-date', 'bg-blue-600', 'text-white', 'hover:bg-blue-700');
                        if (!cell.classList.contains('bg-blue-100')) {
                            cell.classList.add('hover:bg-blue-100', 'dark:hover:bg-blue-900');
                        }
                    });
                    
                    // Add selection to clicked date
                    dayCell.classList.add('selected-date', 'bg-blue-600', 'text-white', 'hover:bg-blue-700');
                    dayCell.classList.remove('hover:bg-blue-100', 'dark:hover:bg-blue-900');
                    
                    // Update hidden input
                    dateInput.value = dateString;
                    console.log('Date selected:', dateString);
                });
            } else {
                dayCell.disabled = true;
            }
            
            grid.appendChild(dayCell);
        }
    }
}
