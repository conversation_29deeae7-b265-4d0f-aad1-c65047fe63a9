import { Controller } from '@hotwired/stimulus';
import { Datepicker } from 'flowbite-datepicker';

export default class extends Controller {
    static values = { bookedDates: Array }

    connect() {
        this.form = this.element.querySelector('form');
        this.deskId = this.element.id.replace('reservationModal-', '');

        if (this.form) {
            this.form.addEventListener('submit', this.handleFormSubmit.bind(this));
        } else {
            console.error('Form not found in modal');
        }

        setTimeout(() => {
            this.initFlowbiteDatepicker();
        }, 100);
    }

    handleFormSubmit(event) {
        const dateInput = this.element.querySelector(`#reservation_date_${this.deskId}`);
        if (!dateInput || !dateInput.value) {
            event.preventDefault();
            alert('Veuillez sélectionner une date pour votre réservation');
            return false;
        }
    }

    initFlowbiteDatepicker() {
        const calendarContainer = this.element.querySelector(`#calendar-container-${this.deskId}`);
        const dateInput = this.element.querySelector(`#reservation_date_${this.deskId}`);

        if (!calendarContainer || !dateInput) {
            console.error('Calendar container or date input not found');
            return;
        }

        // Récupère les jours disponibles
        const availableDays = {
            0: dateInput.dataset.sunday === '1',
            1: dateInput.dataset.monday === '1',
            2: dateInput.dataset.tuesday === '1',
            3: dateInput.dataset.wednesday === '1',
            4: dateInput.dataset.thursday === '1',
            5: dateInput.dataset.friday === '1',
            6: dateInput.dataset.saturday === '1'
        };

        // Dates déjà réservées
        const bookedDates = this.bookedDatesValue || [];

        // Génère les dates à désactiver (passées, réservées, ou jours non disponibles)
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Désactive les jours non disponibles sur 1 an
        let disabledDates = [...bookedDates];
        const addDays = (date, days) => {
            const d = new Date(date);
            d.setDate(d.getDate() + days);
            return d;
        };
        for (let d = new Date(today); d < addDays(today, 366); d.setDate(d.getDate() + 1)) {
            const day = d.getDay();
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const dayStr = String(d.getDate()).padStart(2, '0');
            const dateStr = `${year}-${month}-${dayStr}`;
            if (!availableDays[day] && !disabledDates.includes(dateStr)) {
                disabledDates.push(dateStr);
            }
        }

        // Désactive les dates passées
        const minDate = today.toISOString().slice(0, 10);

        // Ajoute le champ input du datepicker
        calendarContainer.innerHTML = `<input id="datepicker-${this.deskId}" type="text" class="w-full" readonly style="background: white; cursor: pointer;" />`;

        // Initialise le Datepicker Flowbite
        const datepicker = new Datepicker(document.getElementById(`datepicker-${this.deskId}`), {
            format: 'yyyy-mm-dd',
            minDate: minDate,
            datesDisabled: disabledDates,
            autohide: true,
            todayHighlight: true,
            language: 'fr'
        });

        // Synchronise la valeur sélectionnée avec le champ caché
        document.getElementById(`datepicker-${this.deskId}`).addEventListener('changeDate', (event) => {
            dateInput.value = event.target.value;
        });
    }
}